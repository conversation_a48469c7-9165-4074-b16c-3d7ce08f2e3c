import { motion } from "framer-motion";
import { useInView } from "framer-motion";
import { useRef } from "react";

export default function AboutSection() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  const tools = [
    { icon: "fab fa-adobe", name: "Photoshop", color: "#31A8FF" },
    { icon: "fab fa-figma", name: "Figma", color: "#F24E1E" },
    { icon: "fas fa-palette", name: "Canva", color: "#00C4CC" },
    { icon: "fas fa-video", name: "After Effects", color: "#9999FF" },
  ];

  return (
    <section
      id='about'
      className='py-32 px-6 lg:px-12 bg-gradient-to-br from-gray-50 to-white'
      ref={ref}
    >
      <div className='max-w-7xl mx-auto'>
        <div className='grid lg:grid-cols-2 gap-20 items-center'>
          {/* Content */}
          <motion.div
            className='space-y-12'
            initial={{ opacity: 0, x: -50 }}
            animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -50 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
          >
            <div className='space-y-8'>
              <h2 className='text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight'>
                Not Just Design — I{" "}
                <span className='text-gradient'>Engineer Attention</span>
              </h2>

              <p className='text-lg md:text-xl text-gray-600 leading-relaxed'>
                I'm Bilal Ahmed, a results-focused thumbnail designer who thinks
                like a creator and works like a growth strategist. With deep
                insights into human psychology and YouTube algorithms, I design
                visuals that{" "}
                <span className='font-semibold text-[#FF4500]'>
                  demand clicks
                </span>{" "}
                — without clickbait. My speed is unmatched, and my goal is
                simple: help your content win.
              </p>
            </div>

            {/* Tools */}
            <div className='space-y-6'>
              <h3 className='text-2xl font-semibold text-gray-900'>My Tools</h3>
              <div className='grid grid-cols-2 md:grid-cols-4 gap-4'>
                {tools.map((tool, index) => (
                  <motion.div
                    key={tool.name}
                    className='glassmorphic rounded-2xl px-4 py-6 magnetic-hover text-center'
                    initial={{ opacity: 0, y: 20 }}
                    animate={
                      isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }
                    }
                    transition={{ duration: 0.5, delay: 0.3 + index * 0.1 }}
                  >
                    <div className='flex flex-col items-center space-y-3'>
                      <i
                        className={`${tool.icon} text-3xl`}
                        style={{ color: tool.color }}
                      ></i>
                      <span className='font-medium text-sm'>{tool.name}</span>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div>

          {/* Image */}
          <motion.div
            className='relative'
            initial={{ opacity: 0, x: 50 }}
            animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: 50 }}
            transition={{ duration: 0.8, ease: "easeOut", delay: 0.2 }}
          >
            <div className='glassmorphic rounded-3xl p-8 magnetic-hover'>
              <img
                src='/bilal-photo.png'
                alt='Bilal Ahmed - Professional Thumbnail Designer'
                className='rounded-2xl shadow-2xl w-full h-auto object-cover'
              />
              <div className='absolute -bottom-6 left-1/2 transform -translate-x-1/2 bg-gradient-to-r from-[#FF4500] to-[#FF6B35] text-white px-8 py-4 rounded-2xl font-semibold text-center shadow-lg'>
                Proven Results
              </div>
            </div>

            {/* Floating Elements */}
            <motion.div
              className='absolute -top-6 -right-6 w-24 h-24 bg-gradient-to-br from-[#FFA500] to-[#FF4500] rounded-2xl opacity-80'
              animate={{ y: [0, -20, 0] }}
              transition={{ duration: 6, repeat: Infinity, ease: "easeInOut" }}
            />
          </motion.div>
        </div>
      </div>
    </section>
  );
}

import { VercelRequest, VercelResponse } from '@vercel/node';
import { z } from "zod";

// Simple in-memory storage for contacts
interface Contact {
  id: number;
  name: string;
  email: string;
  subject: string;
  message: string;
}

let contacts: Contact[] = [];
let currentId = 1;

// Validation schema
const contactSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Please enter a valid email address"),
  subject: z.string().min(1, "Please select a subject"),
  message: z.string().min(10, "Message must be at least 10 characters"),
});

export default async function handler(req: VercelRequest, res: VercelResponse) {
  // Enable CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  // <PERSON>le preflight requests
  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  const { url, method } = req;

  try {
    // API Routes
    if (url?.startsWith('/api/contact') && method === 'POST') {
      try {
        const validatedData = contactSchema.parse(req.body);
        const contact: Contact = {
          id: currentId++,
          ...validatedData
        };
        contacts.push(contact);

        console.log(`Contact form submitted: ${contact.name} - ${contact.email}`);

        res.status(200).json({
          success: true,
          contact,
          message: "Contact form submitted successfully!"
        });
        return;
      } catch (error) {
        if (error instanceof z.ZodError) {
          res.status(400).json({
            success: false,
            message: "Validation failed",
            errors: error.errors
          });
          return;
        } else {
          throw error;
        }
      }
    }

    // For all other routes, serve the static HTML
    const html = `
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1" />
    <title>Bilal Ahmed - Thumbnail Designer | Bold. Persuasive. Viral.</title>
    <meta name="description" content="Professional thumbnail designer creating scroll-stopping visuals that boost click-through rates. Specialized in YouTube thumbnails with strategy, speed, and creative instinct.">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script type="module" crossorigin src="/assets/index-c-n4PvC2.js"></script>
    <link rel="stylesheet" crossorigin href="/assets/index-DdtNWcUu.css">
  </head>
  <body>
    <div id="root"></div>
  </body>
</html>`;

    res.setHeader('Content-Type', 'text/html');
    res.status(200).send(html);

  } catch (error) {
    console.error('Server error:', error);
    res.status(500).json({
      success: false,
      message: "Internal server error"
    });
  }
}

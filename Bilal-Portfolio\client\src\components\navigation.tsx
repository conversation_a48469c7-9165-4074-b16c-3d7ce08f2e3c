import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

export default function Navigation() {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [activeSection, setActiveSection] = useState('hero');

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
      
      // Update active section based on scroll position
      const sections = ['hero', 'about', 'services', 'portfolio', 'contact'];
      const currentSection = sections.find(section => {
        const element = document.getElementById(section);
        if (element) {
          const rect = element.getBoundingClientRect();
          return rect.top <= 100 && rect.bottom >= 100;
        }
        return false;
      });
      if (currentSection) setActiveSection(currentSection);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const navItems = [
    { href: '#hero', label: 'Home', id: 'hero' },
    { href: '#about', label: 'About', id: 'about' },
    { href: '#services', label: 'Services', id: 'services' },
    { href: '#portfolio', label: 'Portfolio', id: 'portfolio' },
    { href: '#contact', label: 'Contact', id: 'contact' },
  ];

  return (
    <motion.nav 
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-700 ${
        isScrolled ? 'py-3' : 'py-6'
      }`}
      style={{
        background: isScrolled 
          ? 'linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 245, 235, 0.95) 100%)'
          : 'transparent'
      }}
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.8, ease: "easeOut" }}
    >
      <div className="max-w-7xl mx-auto px-6 lg:px-12">
        <motion.div 
          className={`backdrop-blur-2xl border border-white/20 shadow-2xl transition-all duration-700 ${
            isScrolled 
              ? 'rounded-full px-8 py-3 bg-white/80' 
              : 'rounded-3xl px-10 py-5 bg-gradient-to-r from-white/10 via-white/20 to-white/10'
          }`}
          whileHover={{ 
            scale: 1.02,
            boxShadow: "0 20px 40px rgba(255, 69, 0, 0.15)"
          }}
          transition={{ duration: 0.3 }}
        >
          <div className="flex items-center justify-between">
            {/* Logo with magnetic effect */}
            <motion.div 
              className="relative"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
            >
              <div className="flex items-center space-x-3">
                <img 
                  src="/logo.png" 
                  alt="Bilal Ahmed Logo" 
                  className="w-10 h-10"
                />
                <motion.div
                  className="text-2xl font-black bg-gradient-to-r from-[#FF4500] via-[#FF6B35] to-[#FFA500] bg-clip-text text-transparent"
                  animate={{ 
                    backgroundImage: [
                      'linear-gradient(45deg, #FF4500, #FF6B35)',
                      'linear-gradient(135deg, #FF6B35, #FFA500)',
                      'linear-gradient(225deg, #FFA500, #FF4500)',
                      'linear-gradient(315deg, #FF4500, #FF6B35)'
                    ]
                  }}
                  transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
                >
                  Bilal Ahmed
                </motion.div>
              </div>
              <motion.div
                className="absolute -inset-2 bg-gradient-to-r from-[#FF4500] to-[#FFA500] rounded-full opacity-0 blur-xl"
                whileHover={{ opacity: 0.3 }}
                transition={{ duration: 0.3 }}
              />
            </motion.div>
            
            {/* Desktop Menu with advanced animations */}
            <div className="hidden md:flex items-center space-x-2">
              {navItems.map((item, index) => (
                <motion.div 
                  key={item.href}
                  className="relative"
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                >
                  <motion.a
                    href={item.href}
                    className={`relative px-6 py-3 rounded-full font-semibold transition-all duration-300 ${
                      activeSection === item.id
                        ? 'text-white'
                        : 'text-gray-700 hover:text-[#FF4500]'
                    }`}
                    whileHover={{ 
                      scale: 1.05,
                      y: -2
                    }}
                    whileTap={{ scale: 0.95 }}
                  >
                    {item.label}
                    {activeSection === item.id && (
                      <motion.div
                        className="absolute inset-0 bg-gradient-to-r from-[#FF4500] to-[#FF6B35] rounded-full -z-10"
                        layoutId="activeSection"
                        initial={false}
                        transition={{ 
                          type: "spring", 
                          stiffness: 500, 
                          damping: 30 
                        }}
                      />
                    )}
                    <motion.div
                      className="absolute inset-0 bg-gradient-to-r from-[#FF4500] to-[#FF6B35] rounded-full opacity-0 -z-20"
                      whileHover={{ 
                        opacity: 0.1,
                        scale: 1.1
                      }}
                      transition={{ duration: 0.3 }}
                    />
                  </motion.a>
                </motion.div>
              ))}
            </div>
            
            {/* Enhanced Mobile Menu Button */}
            <motion.button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="md:hidden relative w-12 h-12 rounded-full bg-gradient-to-r from-[#FF4500] to-[#FF6B35] text-white flex items-center justify-center"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <motion.div
                animate={isMobileMenuOpen ? { rotate: 180 } : { rotate: 0 }}
                transition={{ duration: 0.3 }}
              >
                <i className={`fas ${isMobileMenuOpen ? 'fa-times' : 'fa-bars'} text-lg`}></i>
              </motion.div>
            </motion.button>
          </div>
        </motion.div>
        
        {/* Enhanced Mobile Menu */}
        <AnimatePresence>
          {isMobileMenuOpen && (
            <motion.div
              className="md:hidden absolute top-full left-6 right-6 mt-4 backdrop-blur-2xl bg-white/90 rounded-3xl border border-white/20 shadow-2xl overflow-hidden"
              initial={{ opacity: 0, y: -20, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -20, scale: 0.95 }}
              transition={{ duration: 0.3, ease: "easeOut" }}
            >
              <div className="p-8">
                <div className="flex flex-col space-y-4">
                  {navItems.map((item, index) => (
                    <motion.a
                      key={item.href}
                      href={item.href}
                      className="relative py-4 px-6 rounded-2xl font-semibold text-gray-700 hover:text-white transition-all duration-300 group"
                      onClick={() => setIsMobileMenuOpen(false)}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.1 }}
                      whileHover={{ x: 10 }}
                    >
                      <motion.div
                        className="absolute inset-0 bg-gradient-to-r from-[#FF4500] to-[#FF6B35] rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                        whileHover={{ scale: 1.02 }}
                      />
                      <span className="relative z-10">{item.label}</span>
                    </motion.a>
                  ))}
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </motion.nav>
  );
}

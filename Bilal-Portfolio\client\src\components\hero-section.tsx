import { motion, useScroll, useTransform } from 'framer-motion';
import { useRef, useState, useEffect } from 'react';

export default function HeroSection() {
  const ref = useRef(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start start", "end start"]
  });
  
  const y = useTransform(scrollYProgress, [0, 1], ["0%", "50%"]);
  const opacity = useTransform(scrollYProgress, [0, 1], [1, 0]);
  const scale = useTransform(scrollYProgress, [0, 1], [1, 0.8]);

  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  useEffect(() => {
    const updateMousePosition = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };
    window.addEventListener('mousemove', updateMousePosition);
    return () => window.removeEventListener('mousemove', updateMousePosition);
  }, []);

  const backgroundVariants = {
    animate: {
      background: [
        "linear-gradient(135deg, #fff 0%, #fff5eb 50%, #fff 100%)",
        "linear-gradient(135deg, #fff5eb 0%, #fff 50%, #ffede0 100%)",
        "linear-gradient(135deg, #ffede0 0%, #fff5eb 50%, #fff 100%)",
      ],
      transition: {
        duration: 8,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  };

  return (
    <motion.section 
      ref={ref}
      id="hero" 
      className="min-h-screen flex items-center justify-center px-6 lg:px-12 py-32 relative overflow-hidden"
      variants={backgroundVariants}
      animate="animate"
    >
      {/* Dynamic Background with Mouse Tracking */}
      <div className="absolute inset-0 z-0">
        {/* Magnetic floating elements */}
        <motion.div 
          className="absolute w-80 h-80 bg-gradient-to-br from-[#FF4500]/20 via-[#FF6B35]/15 to-[#FFA500]/10 rounded-full blur-3xl"
          style={{
            left: mousePosition.x * 0.02 + 100,
            top: mousePosition.y * 0.02 + 100,
          }}
          animate={{ 
            scale: [1, 1.2, 1],
            rotate: [0, 180, 360],
          }}
          transition={{ 
            duration: 20, 
            repeat: Infinity, 
            ease: "easeInOut" 
          }}
        />
        
        <motion.div 
          className="absolute w-60 h-60 bg-gradient-to-tl from-[#FFA500]/15 via-[#FF4500]/20 to-[#FF6B35]/10 rounded-full blur-3xl"
          style={{
            right: mousePosition.x * -0.01 + 150,
            bottom: mousePosition.y * -0.01 + 150,
          }}
          animate={{ 
            scale: [1.2, 1, 1.2],
            rotate: [360, 180, 0],
          }}
          transition={{ 
            duration: 15, 
            repeat: Infinity, 
            ease: "easeInOut",
            delay: -5
          }}
        />

        {/* Geometric floating elements */}
        <motion.div 
          className="absolute top-1/4 left-1/4 w-20 h-20 border-4 border-[#FF4500]/30 rounded-2xl"
          animate={{ 
            rotate: [0, 360],
            y: [0, -30, 0],
            scale: [1, 1.1, 1]
          }}
          transition={{ 
            duration: 8, 
            repeat: Infinity, 
            ease: "easeInOut" 
          }}
        />
        
        <motion.div 
          className="absolute bottom-1/4 right-1/4 w-16 h-16 bg-gradient-to-br from-[#FF6B35] to-[#FFA500] rounded-full"
          animate={{ 
            y: [0, -40, 0],
            x: [0, 20, 0],
            scale: [1, 1.3, 1]
          }}
          transition={{ 
            duration: 6, 
            repeat: Infinity, 
            ease: "easeInOut",
            delay: -2
          }}
        />
      </div>
      
      <motion.div 
        className="max-w-7xl mx-auto text-center z-10 relative"
        style={{ y, opacity, scale }}
      >
        <div className="space-y-12">
          {/* Enhanced Main Headline */}
          <motion.div 
            className="space-y-6"
            initial={{ opacity: 0, y: 100 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, ease: "easeOut" }}
          >
            <div className="relative">
              <motion.h1 
                className="text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-black leading-tight tracking-tight"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 1.2, delay: 0.3 }}
              >
                <motion.span 
                  className="block text-gray-900 mb-2"
                  initial={{ opacity: 0, rotateX: -90 }}
                  animate={{ opacity: 1, rotateX: 0 }}
                  transition={{ duration: 0.8, delay: 0.5 }}
                >
                  Bold.
                </motion.span>
                
                <motion.div 
                  className="relative inline-block mb-2"
                  initial={{ opacity: 0, scale: 0.5 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.8, delay: 0.7, type: "spring", stiffness: 200 }}
                >
                  <motion.span 
                    className="block bg-gradient-to-r from-[#FF4500] via-[#FF6B35] to-[#FFA500] bg-clip-text text-transparent"
                    animate={{ 
                      backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"],
                    }}
                    transition={{ 
                      duration: 3, 
                      repeat: Infinity, 
                      ease: "easeInOut" 
                    }}
                    style={{ backgroundSize: "200% 200%" }}
                  >
                    Persuasive.
                  </motion.span>
                  
                  {/* Glow effect behind text */}
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-[#FF4500] to-[#FFA500] blur-2xl opacity-20 -z-10"
                    animate={{ 
                      scale: [1, 1.2, 1],
                      opacity: [0.2, 0.4, 0.2]
                    }}
                    transition={{ 
                      duration: 2, 
                      repeat: Infinity, 
                      ease: "easeInOut" 
                    }}
                  />
                </motion.div>
                
                <motion.span 
                  className="block text-gray-900"
                  initial={{ opacity: 0, rotateX: -90 }}
                  animate={{ opacity: 1, rotateX: 0 }}
                  transition={{ duration: 0.8, delay: 0.9 }}
                >
                  Viral.
                </motion.span>
              </motion.h1>
            </div>
            
            <motion.p 
              className="text-lg md:text-xl lg:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed font-light"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 1.1 }}
            >
              I design scroll-stopping thumbnails that get clicked — backed by{' '}
              <motion.span 
                className="text-[#FF4500] font-semibold"
                animate={{ scale: [1, 1.05, 1] }}
                transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
              >
                strategy, speed, and creative instinct.
              </motion.span>
            </motion.p>
          </motion.div>
          
          {/* Enhanced CTA Buttons */}
          <motion.div 
            className="flex flex-col sm:flex-row gap-4 items-center justify-center pt-8"
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.3 }}
          >
            <motion.a 
              href="#contact" 
              className="group relative px-8 py-4 bg-gradient-to-r from-[#FF4500] to-[#FF6B35] text-white font-semibold text-lg rounded-full shadow-lg overflow-hidden"
              whileHover={{ 
                scale: 1.02, 
                y: -2,
                boxShadow: "0 15px 30px rgba(255, 69, 0, 0.25)"
              }}
              whileTap={{ scale: 0.98 }}
              transition={{ type: "spring", stiffness: 300, damping: 20 }}
            >
              {/* Subtle shine effect */}
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
                initial={{ x: "-100%" }}
                whileHover={{ x: "100%" }}
                transition={{ duration: 0.6 }}
              />
              
              <span className="relative z-10 flex items-center">
                <span className="mr-2">Let's Make Thumbnails That Convert</span>
                <motion.i 
                  className="fas fa-arrow-right text-sm"
                  animate={{ x: [0, 2, 0] }}
                  transition={{ 
                    duration: 1.5, 
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                />
              </span>
            </motion.a>
            
            <motion.a 
              href="#portfolio" 
              className="group relative px-8 py-4 bg-white text-[#FF4500] font-semibold text-lg rounded-full border-2 border-[#FF4500] shadow-lg hover:bg-[#FF4500] hover:text-white transition-colors duration-300"
              whileHover={{ 
                scale: 1.02, 
                y: -2
              }}
              whileTap={{ scale: 0.98 }}
              transition={{ type: "spring", stiffness: 300, damping: 20 }}
            >
              <span className="relative z-10 flex items-center">
                <span className="mr-2">See My Work</span>
                <motion.i 
                  className="fas fa-eye text-sm"
                  animate={{ scale: [1, 1.1, 1] }}
                  transition={{ 
                    duration: 2, 
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                />
              </span>
            </motion.a>
          </motion.div>
          
          {/* Floating Statistics */}
          <motion.div 
            className="grid grid-cols-2 md:grid-cols-4 gap-6 pt-12"
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.5 }}
          >
            {[
              { number: "500+", label: "Thumbnails Created" },
              { number: "85%", label: "Avg CTR Increase" },
              { number: "50+", label: "Happy Creators" },
              { number: "2M+", label: "Views Generated" }
            ].map((stat, index) => (
              <motion.div
                key={index}
                className="text-center glassmorphic rounded-2xl p-4"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 1.7 + index * 0.1 }}
                whileHover={{ 
                  scale: 1.05,
                  boxShadow: "0 15px 30px rgba(255, 69, 0, 0.1)"
                }}
              >
                <motion.h3 
                  className="text-2xl md:text-3xl font-black text-[#FF4500] mb-2"
                  animate={{ scale: [1, 1.05, 1] }}
                  transition={{ 
                    duration: 2, 
                    repeat: Infinity, 
                    ease: "easeInOut",
                    delay: index * 0.2
                  }}
                >
                  {stat.number}
                </motion.h3>
                <p className="text-gray-600 font-medium text-sm">{stat.label}</p>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </motion.div>
    </motion.section>
  );
}

import { motion } from 'framer-motion';
import { useInView } from 'framer-motion';
import { useRef } from 'react';

export default function TestimonialsSection() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  const testimonials = [
    {
      text: "<PERSON><PERSON><PERSON> isn't just fast — he understands what makes people click. My views shot up 40% after switching to his thumbnails!",
      name: "<PERSON>",
      role: "Tech YouTuber • 250K Subscribers",
      avatar: "A"
    },
    {
      text: "Professional, creative, and results-driven. <PERSON><PERSON><PERSON> doesn't just create beautiful thumbnails – he creates thumbnails that convert viewers into subscribers.",
      name: "<PERSON>",
      role: "Fitness Influencer • 180K Subscribers",
      avatar: "S"
    },
    {
      text: "Working with <PERSON><PERSON><PERSON> was a game-changer. His thumbnails not only look amazing but also perfectly capture the essence of my content. Highly recommended!",
      name: "<PERSON>",
      role: "Gaming Creator • 500K Subscribers",
      avatar: "<PERSON>"
    }
  ];

  return (
    <section className="py-32 px-6 lg:px-12 bg-gradient-to-br from-gray-50 to-white" ref={ref}>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div 
          className="text-center space-y-8 mb-20"
          initial={{ opacity: 0, y: 50 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight">
            <span className="text-gradient">Loved by Creators</span>, Trusted by Brands
          </h2>
          <p className="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Real feedback from creators who've seen their channels grow with my thumbnail designs.
          </p>
        </motion.div>
        
        {/* Testimonials Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <motion.div
              key={index}
              className="glassmorphic rounded-3xl p-8 magnetic-hover"
              initial={{ opacity: 0, y: 30 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
              transition={{ duration: 0.6, delay: 0.2 * index }}
            >
              <div className="space-y-6">
                <div className="flex text-[#FF4500] text-xl">
                  {[...Array(5)].map((_, i) => (
                    <i key={i} className="fas fa-star"></i>
                  ))}
                </div>
                <p className="text-gray-600 leading-relaxed">
                  "{testimonial.text}"
                </p>
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-[#FF4500] to-[#FF6B35] rounded-full flex items-center justify-center text-white font-bold">
                    {testimonial.avatar}
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900">{testimonial.name}</h4>
                    <p className="text-gray-500 text-sm">{testimonial.role}</p>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}

Stack trace:
Frame         Function      Args
0007FFFF9CF0  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF8BF0) msys-2.0.dll+0x2118E
0007FFFF9CF0  0002100469BA (000000000000, 000000000000, 000000000000, 0007FFFF9FC8) msys-2.0.dll+0x69BA
0007FFFF9CF0  0002100469F2 (00021028DF99, 0007FFFF9BA8, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFF9CF0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF9CF0  00021006A545 (0007FFFF9D00, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0007FFFF9FD0  00021006B9A5 (0007FFFF9D00, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFCCFA20000 ntdll.dll
7FFCCEBF0000 KERNEL32.DLL
7FFCCCE50000 KERNELBASE.dll
7FFCCF550000 USER32.dll
7FFCCCB70000 win32u.dll
7FFCCE240000 GDI32.dll
7FFCCCD10000 gdi32full.dll
000210040000 msys-2.0.dll
7FFCCCBA0000 msvcp_win.dll
7FFCCD3C0000 ucrtbase.dll
7FFCCF010000 advapi32.dll
7FFCCE190000 msvcrt.dll
7FFCCE270000 sechost.dll
7FFCCDFF0000 RPCRT4.dll
7FFCCC200000 CRYPTBASE.DLL
7FFCCD510000 bcryptPrimitives.dll
7FFCCEF30000 IMM32.DLL

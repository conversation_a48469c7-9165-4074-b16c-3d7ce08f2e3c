import { motion } from "framer-motion";
import { useState } from "react";

export default function Footer() {
  const [email, setEmail] = useState("");

  const socialLinks = [
    {
      icon: "fab fa-instagram",
      href: "#",
      label: "Instagram",
      color: "from-pink-500 to-purple-600",
    },
    {
      icon: "fab fa-linkedin",
      href: "#",
      label: "LinkedIn",
      color: "from-blue-600 to-blue-700",
    },
    {
      icon: "fab fa-youtube",
      href: "#",
      label: "YouTube",
      color: "from-red-500 to-red-600",
    },
    {
      icon: "fab fa-twitter",
      href: "#",
      label: "Twitter",
      color: "from-blue-400 to-blue-500",
    },
  ];

  const quickLinks = [
    { name: "Home", href: "#hero" },
    { name: "About", href: "#about" },
    { name: "Portfolio", href: "#portfolio" },
    { name: "Contact", href: "#contact" },
  ];

  const services = [
    "YouTube Thumbnails",
    "Social Media Graphics",
    "Brand Design",
    "Content Strategy",
  ];

  return (
    <footer className='relative bg-gradient-to-br from-gray-900 via-gray-800 to-black text-white overflow-hidden'>
      {/* Background Pattern */}
      <div className='absolute inset-0 opacity-5'>
        <div
          className='absolute inset-0'
          style={{
            backgroundImage: `radial-gradient(circle at 2px 2px, white 1px, transparent 0)`,
            backgroundSize: "40px 40px",
          }}
        />
      </div>

      {/* Floating Background Elements */}
      <motion.div
        className='absolute top-10 left-10 w-32 h-32 bg-gradient-to-br from-[#FF4500]/20 to-[#FF6B35]/10 rounded-full blur-3xl'
        animate={{
          y: [0, -20, 0],
          scale: [1, 1.2, 1],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />

      <motion.div
        className='absolute bottom-10 right-10 w-24 h-24 bg-gradient-to-tl from-[#FFA500]/20 to-[#FF4500]/10 rounded-full blur-2xl'
        animate={{
          y: [0, 20, 0],
          x: [0, -10, 0],
        }}
        transition={{
          duration: 6,
          repeat: Infinity,
          ease: "easeInOut",
          delay: -2,
        }}
      />

      <div className='relative z-10 max-w-7xl mx-auto px-6 lg:px-12'>
        {/* Main Footer Content */}
        <div className='grid lg:grid-cols-4 md:grid-cols-2 gap-12 py-20'>
          {/* Brand Section */}
          <motion.div
            className='space-y-6'
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <motion.h3
              className='text-4xl font-black bg-gradient-to-r from-[#FF4500] via-[#FF6B35] to-[#FFA500] bg-clip-text text-transparent'
              animate={{
                backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"],
              }}
              transition={{
                duration: 4,
                repeat: Infinity,
                ease: "easeInOut",
              }}
              style={{ backgroundSize: "200% 200%" }}
            >
              Bilal Ahmed
            </motion.h3>
            <p className='text-gray-300 leading-relaxed'>
              Creating scroll-stopping thumbnails that turn viewers into
              subscribers. Let's make your content impossible to ignore.
            </p>

            {/* Newsletter Signup */}
            <div className='space-y-4'>
              <h4 className='text-lg font-semibold text-white'>Stay Updated</h4>
              <div className='flex flex-col sm:flex-row gap-3'>
                <input
                  type='email'
                  placeholder='Enter your email'
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className='flex-1 px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#FF4500] focus:border-transparent backdrop-blur-sm'
                />
                <motion.button
                  className='px-6 py-3 bg-gradient-to-r from-[#FF4500] to-[#FF6B35] text-white font-semibold rounded-xl hover:shadow-lg transition-all duration-300'
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Subscribe
                </motion.button>
              </div>
            </div>
          </motion.div>

          {/* Quick Links */}
          <motion.div
            className='space-y-6'
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            viewport={{ once: true }}
          >
            <h4 className='text-xl font-bold text-white'>Quick Links</h4>
            <ul className='space-y-3'>
              {quickLinks.map((link, index) => (
                <motion.li key={index}>
                  <motion.a
                    href={link.href}
                    className='text-gray-300 hover:text-[#FF4500] transition-colors duration-300 flex items-center group'
                    whileHover={{ x: 5 }}
                  >
                    <motion.i
                      className='fas fa-chevron-right text-xs mr-3 group-hover:text-[#FF4500]'
                      animate={{ x: [0, 3, 0] }}
                      transition={{
                        duration: 1.5,
                        repeat: Infinity,
                        ease: "easeInOut",
                      }}
                    />
                    {link.name}
                  </motion.a>
                </motion.li>
              ))}
            </ul>
          </motion.div>

          {/* Services */}
          <motion.div
            className='space-y-6'
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <h4 className='text-xl font-bold text-white'>Services</h4>
            <ul className='space-y-3'>
              {services.map((service, index) => (
                <motion.li
                  key={index}
                  className='text-gray-300 flex items-center'
                  whileHover={{ x: 5 }}
                >
                  <div className='w-2 h-2 bg-gradient-to-r from-[#FF4500] to-[#FF6B35] rounded-full mr-3'></div>
                  {service}
                </motion.li>
              ))}
            </ul>
          </motion.div>

          {/* Contact Info */}
          <motion.div
            className='space-y-6'
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            viewport={{ once: true }}
          >
            <h4 className='text-xl font-bold text-white'>Get In Touch</h4>
            <div className='space-y-4'>
              <motion.div
                className='flex items-center space-x-3 text-gray-300'
                whileHover={{ x: 5 }}
              >
                <div className='w-12 h-12 bg-gradient-to-r from-[#EA4335] to-[#FBBC05] rounded-lg flex items-center justify-center flex-shrink-0'>
                  <i className='fas fa-at text-white text-lg'></i>
                </div>
                <span><EMAIL></span>
              </motion.div>

              <motion.div
                className='flex items-center space-x-3 text-gray-300'
                whileHover={{ x: 5 }}
              >
                <div className='w-12 h-12 bg-gradient-to-r from-[#FF4500] to-[#FF6B35] rounded-lg flex items-center justify-center flex-shrink-0'>
                  <i className='fab fa-whatsapp text-white text-lg'></i>
                </div>
                <span>+92 334 0505686</span>
              </motion.div>
            </div>
          </motion.div>
        </div>

        {/* Bottom Section */}
        <motion.div
          className='border-t border-white/10 py-8'
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <div className='flex flex-col md:flex-row items-center justify-between space-y-6 md:space-y-0'>
            {/* Copyright */}
            <div className='text-center md:text-left'>
              <p className='text-gray-400'>
                © 2025 Bilal Ahmed | Thumbnail Designer. Crafted with passion.
                All rights reserved.
              </p>
            </div>

            {/* Enhanced Social Links */}
            <div className='flex items-center space-x-4'>
              {socialLinks.map((link, index) => (
                <motion.a
                  key={index}
                  href={link.href}
                  className={`group relative w-12 h-12 bg-gradient-to-r ${link.color} rounded-xl flex items-center justify-center overflow-hidden`}
                  whileHover={{
                    scale: 1.1,
                    rotate: 5,
                    y: -3,
                  }}
                  whileTap={{ scale: 0.9 }}
                  transition={{ type: "spring", stiffness: 300, damping: 20 }}
                >
                  {/* Hover glow effect */}
                  <motion.div
                    className={`absolute inset-0 bg-gradient-to-r ${link.color} blur-lg opacity-0 group-hover:opacity-50 -z-10`}
                    transition={{ duration: 0.3 }}
                  />

                  <motion.i
                    className={`${link.icon} text-white relative z-10`}
                    animate={{
                      rotate: [0, 5, -5, 0],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut",
                      delay: index * 0.2,
                    }}
                  />

                  {/* Tooltip */}
                  <motion.div
                    className='absolute -top-12 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap'
                    initial={{ y: 10, opacity: 0 }}
                    whileHover={{ y: 0, opacity: 1 }}
                  >
                    {link.label}
                    <div className='absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800'></div>
                  </motion.div>
                </motion.a>
              ))}
            </div>
          </div>
        </motion.div>
      </div>
    </footer>
  );
}

# Thumbnail Designer <PERSON><PERSON><PERSON>

## Overview

This is a modern, fully responsive portfolio website for a thumbnail designer named <PERSON><PERSON><PERSON>. The application showcases a professional portfolio with smooth animations, contact functionality, and a clean design focused on converting visitors into clients. The site features a minimalist layout with bold orange accents and is built using React with TypeScript, Express.js backend, and PostgreSQL database.

## System Architecture

### Frontend Architecture
- **Framework**: React 18 with TypeScript
- **Styling**: Tailwind CSS with custom CSS variables for theming
- **UI Components**: Radix UI primitives with shadcn/ui component system
- **Animations**: Framer Motion for smooth transitions and micro-interactions
- **Routing**: Wouter for lightweight client-side routing
- **State Management**: TanStack Query for server state management
- **Form Handling**: React Hook Form with Zod validation

### Backend Architecture
- **Framework**: Express.js with TypeScript
- **Database**: PostgreSQL with Drizzle ORM
- **Database Provider**: Neon Database (serverless PostgreSQL)
- **Session Management**: connect-pg-simple for PostgreSQL session storage
- **API Design**: RESTful API endpoints under `/api` prefix
- **Error Handling**: Centralized error middleware with proper HTTP status codes

### Build System
- **Bundler**: Vite for frontend development and building
- **Backend Bundler**: esbuild for server-side code bundling
- **TypeScript**: Shared types between frontend and backend via shared schema
- **Development**: Hot module replacement with Vite dev server

## Key Components

### Database Layer
- **Schema**: Defined in `shared/schema.ts` using Drizzle ORM
- **Tables**: Contacts table for storing form submissions
- **Validation**: Zod schemas for type-safe data validation
- **Migration**: Drizzle Kit for database schema management

### API Layer
- **Contact Endpoint**: POST `/api/contact` for form submissions
- **Validation**: Server-side validation using Zod schemas
- **Error Handling**: Proper error responses with validation details

### UI Components
- **Navigation**: Sticky header with smooth scroll navigation
- **Hero Section**: Large headline with animated background elements
- **About Section**: Tool showcase with glassmorphic design cards
- **Portfolio**: Grid layout with hover effects and category tags
- **Testimonials**: Client feedback with animated cards
- **Contact Form**: Validated form with toast notifications
- **Footer**: Social links and copyright information

### Design System
- **Colors**: Orange-based theme (#FF4500 primary, #FF6B35 secondary)
- **Typography**: Poppins font family for modern aesthetic
- **Layout**: Responsive grid system with mobile-first approach
- **Animations**: Smooth transitions and hover effects throughout

## Data Flow

1. **User Interaction**: User navigates the portfolio and fills out contact form
2. **Form Submission**: Contact form data is validated client-side with React Hook Form + Zod
3. **API Request**: Form data sent to `/api/contact` endpoint via TanStack Query mutation
4. **Server Validation**: Express server validates data using shared Zod schema
5. **Database Storage**: Contact information stored in PostgreSQL via Drizzle ORM
6. **Response**: Success/error response sent back to client
7. **UI Feedback**: Toast notification shown to user based on response

## External Dependencies

### Frontend Dependencies
- **UI Framework**: React with TypeScript
- **Styling**: Tailwind CSS, Radix UI components
- **Animations**: Framer Motion
- **Forms**: React Hook Form, Zod validation
- **HTTP Client**: TanStack Query with built-in fetch
- **Icons**: Font Awesome (loaded via CDN)
- **Fonts**: Google Fonts (Poppins)

### Backend Dependencies
- **Server**: Express.js with TypeScript support
- **Database**: Drizzle ORM with Neon PostgreSQL
- **Validation**: Zod for schema validation
- **Session**: connect-pg-simple for session management
- **Development**: tsx for TypeScript execution

### Development Tools
- **Build**: Vite with React plugin
- **Bundling**: esbuild for server code
- **TypeScript**: Shared configuration across client/server
- **Linting**: TypeScript compiler for type checking
- **Database**: Drizzle Kit for migrations

## Deployment Strategy

### Development Environment
- **Command**: `npm run dev` starts both frontend and backend
- **Frontend**: Vite dev server with HMR on port from Vite config
- **Backend**: Express server on port 5000 with live reload via tsx
- **Database**: Neon serverless PostgreSQL connection

### Production Build
- **Frontend**: `vite build` creates optimized static assets in `dist/public`
- **Backend**: `esbuild` bundles server code to `dist/index.js`
- **Assets**: Static files served by Express in production
- **Database**: Same Neon PostgreSQL instance for production

### Replit Deployment
- **Platform**: Configured for Replit autoscale deployment
- **Build Command**: `npm run build` (frontend + backend bundling)
- **Start Command**: `npm run start` (runs production server)
- **Port Mapping**: Internal port 5000 mapped to external port 80
- **Environment**: NODE_ENV=production for optimized performance

### Environment Configuration
- **Database URL**: Required environment variable for PostgreSQL connection
- **Sessions**: Configured to use PostgreSQL for session storage
- **CORS**: Configured for cross-origin requests in development
- **Static Files**: Served from `dist/public` in production

## Changelog

```
Changelog:
- June 24, 2025. Initial setup
- June 24, 2025. Updated branding to Bilal Ahmed, integrated logo and personal image
- June 24, 2025. Enhanced button animations and improved color combinations
- June 24, 2025. Added Services and Problem-Solution sections with new content structure
```

## User Preferences

```
Preferred communication style: Simple, everyday language.
```
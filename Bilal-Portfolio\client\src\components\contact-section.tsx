import { motion } from "framer-motion";
import { useInView } from "framer-motion";
import { useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";

// Contact form schema for frontend validation
const contactSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Please enter a valid email address"),
  subject: z.string().min(1, "Please select a subject"),
  message: z.string().min(10, "Message must be at least 10 characters"),
});

type ContactForm = z.infer<typeof contactSchema>;

export default function ContactSection() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<ContactForm>({
    resolver: zodResolver(contactSchema),
    defaultValues: {
      name: "",
      email: "",
      subject: "",
      message: "",
    },
  });

  const onSubmit = async (data: ContactForm) => {
    setIsSubmitting(true);

    // Simulate form submission delay
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // Create mailto link with form data
    const subject = encodeURIComponent(
      `${data.subject} - Contact from ${data.name}`
    );
    const body = encodeURIComponent(
      `Name: ${data.name}\nEmail: ${data.email}\nSubject: ${data.subject}\n\nMessage:\n${data.message}`
    );
    const mailtoLink = `mailto:<EMAIL>?subject=${subject}&body=${body}`;

    // Open email client
    window.open(mailtoLink, "_blank");

    // Show success message
    toast({
      title: "Ready to send!",
      description:
        "Your email client should open with the message pre-filled. You can also contact me <NAME_EMAIL>",
    });

    form.reset();
    setIsSubmitting(false);
  };

  return (
    <section id='contact' className='py-32 px-6 lg:px-12 bg-white' ref={ref}>
      <div className='max-w-7xl mx-auto'>
        <div className='grid lg:grid-cols-2 gap-20 items-center'>
          {/* Content */}
          <motion.div
            className='space-y-12'
            initial={{ opacity: 0, x: -50 }}
            animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -50 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
          >
            <div className='space-y-8'>
              <h2 className='text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight'>
                Let's Make Your Channel{" "}
                <span className='text-gradient'>Unstoppable.</span>
              </h2>

              <p className='text-lg md:text-xl text-gray-600 leading-relaxed'>
                Ready to see your click-through rates soar? Let's create
                thumbnails that turn browsers into viewers and viewers into
                subscribers.
              </p>
            </div>

            {/* Contact Info */}
            <div className='space-y-6'>
              <div className='flex items-center space-x-4'>
                <div className='w-12 h-12 bg-gradient-to-r from-[#FF4500] to-[#FF6B35] rounded-full flex items-center justify-center text-white'>
                  <i className='fas fa-envelope'></i>
                </div>
                <div>
                  <h4 className='font-semibold text-gray-900'>Email</h4>
                  <p className='text-gray-600'><EMAIL></p>
                </div>
              </div>

              <div className='flex items-center space-x-4'>
                <div className='w-12 h-12 bg-gradient-to-r from-[#FF4500] to-[#FF6B35] rounded-full flex items-center justify-center text-white'>
                  <i className='fab fa-whatsapp'></i>
                </div>
                <div>
                  <h4 className='font-semibold text-gray-900'>WhatsApp</h4>
                  <p className='text-gray-600'>+1 (555) 123-4567</p>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Contact Form */}
          <motion.div
            className='glassmorphic rounded-3xl p-8'
            initial={{ opacity: 0, x: 50 }}
            animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: 50 }}
            transition={{ duration: 0.8, ease: "easeOut", delay: 0.2 }}
          >
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className='space-y-8'
              >
                <div className='grid md:grid-cols-2 gap-6'>
                  <FormField
                    control={form.control}
                    name='name'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className='text-gray-700 font-medium'>
                          Name
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder='Your Name'
                            className='px-6 py-4 border border-gray-200 rounded-2xl focus:ring-2 focus:ring-[#FF4500] focus:border-transparent transition-all duration-300 bg-white/80 backdrop-blur-sm'
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name='email'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className='text-gray-700 font-medium'>
                          Email
                        </FormLabel>
                        <FormControl>
                          <Input
                            type='email'
                            placeholder='<EMAIL>'
                            className='px-6 py-4 border border-gray-200 rounded-2xl focus:ring-2 focus:ring-[#FF4500] focus:border-transparent transition-all duration-300 bg-white/80 backdrop-blur-sm'
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name='subject'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className='text-gray-700 font-medium'>
                        Subject
                      </FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger className='px-6 py-4 border border-gray-200 rounded-2xl focus:ring-2 focus:ring-[#FF4500] focus:border-transparent transition-all duration-300 bg-white/80 backdrop-blur-sm'>
                            <SelectValue placeholder='Select a subject' />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value='thumbnail'>
                            Thumbnail Design
                          </SelectItem>
                          <SelectItem value='consultation'>
                            Design Consultation
                          </SelectItem>
                          <SelectItem value='collaboration'>
                            Long-term Collaboration
                          </SelectItem>
                          <SelectItem value='other'>Other</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name='message'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className='text-gray-700 font-medium'>
                        Message
                      </FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder='Tell me about your project and goals...'
                          rows={6}
                          className='px-6 py-4 border border-gray-200 rounded-2xl focus:ring-2 focus:ring-[#FF4500] focus:border-transparent transition-all duration-300 bg-white/80 backdrop-blur-sm resize-none'
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <Button
                  type='submit'
                  disabled={isSubmitting}
                  className='w-full bg-gradient-to-r from-[#FF4500] to-[#FF6B35] text-white font-semibold text-lg py-6 rounded-2xl hover:shadow-2xl hover:scale-105 transition-all duration-300 flex items-center justify-center space-x-3'
                >
                  <span>{isSubmitting ? "Preparing..." : "Send Message"}</span>
                  <i className='fas fa-paper-plane'></i>
                </Button>
              </form>
            </Form>
          </motion.div>
        </div>
      </div>
    </section>
  );
}

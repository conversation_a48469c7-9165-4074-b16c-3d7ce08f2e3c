import { motion } from "framer-motion";
import { useInView } from "framer-motion";
import { useRef } from "react";

export default function ServicesSection() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  const services = [
    {
      icon: "fas fa-bullseye",
      title: "Custom Thumbnail Design",
      description: "Tailored to your niche and audience",
      color: "from-orange-500 to-red-500",
    },
    {
      icon: "fas fa-bolt",
      title: "24-Hour Delivery Available",
      description: "For urgent uploads",
      color: "from-yellow-500 to-orange-500",
    },
    {
      icon: "fas fa-chart-line",
      title: "A/B Testing Versions",
      description: "Multiple versions to optimize CTR",
      color: "from-green-500 to-teal-500",
    },
    {
      icon: "fas fa-brain",
      title: "Strategy + Design Combo",
      description: "Thumbnail + title brainstorming",
      color: "from-purple-500 to-pink-500",
    },
    {
      icon: "fas fa-folder",
      title: "Thumbnail Pack Deals",
      description: "Bulk thumbnails for serious creators",
      color: "from-blue-500 to-indigo-500",
    },
  ];

  return (
    <section id='services' className='py-32 px-6 lg:px-12 bg-white' ref={ref}>
      <div className='max-w-7xl mx-auto'>
        {/* Header */}
        <motion.div
          className='text-center space-y-8 mb-20'
          initial={{ opacity: 0, y: 50 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          <h2 className='text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight'>
            What I <span className='text-gradient'>Deliver</span>
          </h2>
          <p className='text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed'>
            Professional thumbnail design services that help creators boost
            their click-through rates and grow their channels.
          </p>
        </motion.div>

        {/* Services Grid */}
        <div className='grid md:grid-cols-2 lg:grid-cols-3 gap-8 items-start'>
          {services.map((service, index) => (
            <motion.div
              key={index}
              className='group relative glassmorphic rounded-3xl p-8 hover:shadow-2xl transition-all duration-500 h-full flex flex-col'
              initial={{ opacity: 0, y: 30 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
              transition={{ duration: 0.5, delay: 0.1 * index }}
              whileHover={{
                scale: 1.05,
                y: -10,
              }}
            >
              {/* Icon */}
              <motion.div
                className={`w-16 h-16 rounded-2xl bg-gradient-to-r ${service.color} flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}
              >
                <i className={`${service.icon} text-white text-2xl`}></i>
              </motion.div>

              {/* Content */}
              <div className='space-y-4'>
                <h3 className='text-xl font-bold text-gray-900 group-hover:text-[#FF4500] transition-colors duration-300'>
                  {service.title}
                </h3>
                <p className='text-gray-600 leading-relaxed'>
                  {service.description}
                </p>
              </div>

              {/* Hover glow effect */}
              <motion.div
                className={`absolute inset-0 bg-gradient-to-r ${service.color} rounded-3xl opacity-0 group-hover:opacity-10 -z-10 transition-opacity duration-300`}
              />
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}

{"version": 3, "file": "effect-flip.mjs.mjs", "names": ["createShadow", "effectInit", "effect<PERSON>arget", "effectVirtualTransitionEnd", "getSlideTransformEl", "getRotateFix", "EffectFlip", "_ref", "swiper", "extendParams", "on", "flipEffect", "slideShadows", "limitRotation", "createSlideShadows", "slideEl", "progress", "shadowBefore", "isHorizontal", "querySelector", "shadowAfter", "style", "opacity", "Math", "max", "effect", "setTranslate", "slides", "rtlTranslate", "rtl", "params", "rotateFix", "i", "length", "min", "offset", "swiperSlideOffset", "rotateY", "rotateX", "tx", "cssMode", "translate", "ty", "zIndex", "abs", "round", "transform", "setTransition", "duration", "transformElements", "map", "for<PERSON>ach", "el", "transitionDuration", "querySelectorAll", "shadowEl", "recreateShadows", "getEffectParams", "perspective", "overwriteParams", "<PERSON><PERSON><PERSON><PERSON>iew", "slidesPerGroup", "watchSlidesProgress", "spaceBetween", "virtualTranslate"], "sources": ["0"], "mappings": "YAAcA,iBAAoB,8CACpBC,eAAkB,4CAClBC,iBAAoB,8CACpBC,+BAAkC,8DAClCC,yBAA0BC,iBAAoB,0BAE5D,SAASC,WAAWC,GAClB,IAAIC,OACFA,EAAMC,aACNA,EAAYC,GACZA,GACEH,EACJE,EAAa,CACXE,WAAY,CACVC,cAAc,EACdC,eAAe,KAGnB,MAAMC,EAAqB,CAACC,EAASC,KACnC,IAAIC,EAAeT,EAAOU,eAAiBH,EAAQI,cAAc,6BAA+BJ,EAAQI,cAAc,4BAClHC,EAAcZ,EAAOU,eAAiBH,EAAQI,cAAc,8BAAgCJ,EAAQI,cAAc,+BACjHF,IACHA,EAAejB,aAAa,OAAQe,EAASP,EAAOU,eAAiB,OAAS,QAE3EE,IACHA,EAAcpB,aAAa,OAAQe,EAASP,EAAOU,eAAiB,QAAU,WAE5ED,IAAcA,EAAaI,MAAMC,QAAUC,KAAKC,KAAKR,EAAU,IAC/DI,IAAaA,EAAYC,MAAMC,QAAUC,KAAKC,IAAIR,EAAU,GAAE,EA+DpEf,WAAW,CACTwB,OAAQ,OACRjB,SACAE,KACAgB,aAtDmB,KACnB,MAAMC,OACJA,EACAC,aAAcC,GACZrB,EACEsB,EAAStB,EAAOsB,OAAOnB,WACvBoB,EAAY1B,aAAaG,GAC/B,IAAK,IAAIwB,EAAI,EAAGA,EAAIL,EAAOM,OAAQD,GAAK,EAAG,CACzC,MAAMjB,EAAUY,EAAOK,GACvB,IAAIhB,EAAWD,EAAQC,SACnBR,EAAOsB,OAAOnB,WAAWE,gBAC3BG,EAAWO,KAAKC,IAAID,KAAKW,IAAInB,EAAQC,SAAU,IAAK,IAEtD,MAAMmB,EAASpB,EAAQqB,kBAEvB,IAAIC,GADY,IAAMrB,EAElBsB,EAAU,EACVC,EAAK/B,EAAOsB,OAAOU,SAAWL,EAAS3B,EAAOiC,WAAaN,EAC3DO,EAAK,EACJlC,EAAOU,eAKDW,IACTQ,GAAWA,IALXK,EAAKH,EACLA,EAAK,EACLD,GAAWD,EACXA,EAAU,GAIZtB,EAAQM,MAAMsB,QAAUpB,KAAKqB,IAAIrB,KAAKsB,MAAM7B,IAAaW,EAAOM,OAC5DH,EAAOlB,cACTE,EAAmBC,EAASC,GAE9B,MAAM8B,EAAY,eAAeP,QAASG,qBAAsBX,EAAUO,kBAAwBP,EAAUM,SAC3FnC,aAAa4B,EAAQf,GAC7BM,MAAMyB,UAAYA,CAC7B,GAqBAC,cAnBoBC,IACpB,MAAMC,EAAoBzC,EAAOmB,OAAOuB,KAAInC,GAAWX,oBAAoBW,KAC3EkC,EAAkBE,SAAQC,IACxBA,EAAG/B,MAAMgC,mBAAqB,GAAGL,MACjCI,EAAGE,iBAAiB,gHAAgHH,SAAQI,IAC1IA,EAASlC,MAAMgC,mBAAqB,GAAGL,KAAY,GACnD,IAEJ7C,2BAA2B,CACzBK,SACAwC,WACAC,qBACA,EAQFO,gBAnEsB,KAEtBhD,EAAOsB,OAAOnB,WACdH,EAAOmB,OAAOwB,SAAQpC,IACpB,IAAIC,EAAWD,EAAQC,SACnBR,EAAOsB,OAAOnB,WAAWE,gBAC3BG,EAAWO,KAAKC,IAAID,KAAKW,IAAInB,EAAQC,SAAU,IAAK,IAEtDF,EAAmBC,EAASC,EAAS,GACrC,EA2DFyC,gBAAiB,IAAMjD,EAAOsB,OAAOnB,WACrC+C,YAAa,KAAM,EACnBC,gBAAiB,KAAM,CACrBC,cAAe,EACfC,eAAgB,EAChBC,qBAAqB,EACrBC,aAAc,EACdC,kBAAmBxD,EAAOsB,OAAOU,WAGvC,QAESlC"}
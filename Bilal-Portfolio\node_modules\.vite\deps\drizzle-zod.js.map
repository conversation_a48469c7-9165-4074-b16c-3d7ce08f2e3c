{"version": 3, "sources": ["../../src/logger.ts", "../../src/constants.ts", "../../src/utils.ts", "../../src/column.ts", "../../src/schema.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\n\nexport interface Logger {\n\tlogQuery(query: string, params: unknown[]): void;\n}\n\nexport interface LogWriter {\n\twrite(message: string): void;\n}\n\nexport class ConsoleLogWriter implements LogWriter {\n\tstatic readonly [entityKind]: string = 'ConsoleLogWriter';\n\n\twrite(message: string) {\n\t\tconsole.log(message);\n\t}\n}\n\nexport class DefaultLogger implements Logger {\n\tstatic readonly [entityKind]: string = 'DefaultLogger';\n\n\treadonly writer: LogWriter;\n\n\tconstructor(config?: { writer: LogWriter }) {\n\t\tthis.writer = config?.writer ?? new ConsoleLogWriter();\n\t}\n\n\tlogQuery(query: string, params: unknown[]): void {\n\t\tconst stringifiedParams = params.map((p) => {\n\t\t\ttry {\n\t\t\t\treturn JSON.stringify(p);\n\t\t\t} catch {\n\t\t\t\treturn String(p);\n\t\t\t}\n\t\t});\n\t\tconst paramsStr = stringifiedParams.length ? ` -- params: [${stringifiedParams.join(', ')}]` : '';\n\t\tthis.writer.write(`Query: ${query}${paramsStr}`);\n\t}\n}\n\nexport class NoopLogger implements Logger {\n\tstatic readonly [entityKind]: string = 'NoopLogger';\n\n\tlogQuery(): void {\n\t\t// noop\n\t}\n}\n", null, null, null, null], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAWkB;AADX,IAAM,mBAAN,MAA4C;EAGlD,MAAM,SAAiB;AACtB,YAAQ,IAAI,OAAO;EACpB;AACD;AALC,cADY,kBACK,IAAsB;AAXxC,IAAAA;AAmBkBA,MAAA;AADX,IAAM,gBAAN,MAAsC;EAK5C,YAAY,QAAgC;AAFnC;AAGR,SAAK,UAAS,iCAAQ,WAAU,IAAI,iBAAiB;EACtD;EAEA,SAAS,OAAe,QAAyB;AAChD,UAAM,oBAAoB,OAAO,IAAI,CAAC,MAAM;AAC3C,UAAI;AACH,eAAO,KAAK,UAAU,CAAC;MACxB,QAAQ;AACP,eAAO,OAAO,CAAC;MAChB;IACD,CAAC;AACD,UAAM,YAAY,kBAAkB,SAAS,gBAAgB,kBAAkB,KAAK,IAAI,CAAC,MAAM;AAC/F,SAAK,OAAO,MAAM,UAAU,KAAK,GAAG,SAAS,EAAE;EAChD;AACD;AAnBC,cADY,eACKA,KAAsB;AAnBxC,IAAAA;AAyCkBA,MAAA;AADX,IAAM,aAAN,MAAmC;EAGzC,WAAiB;EAEjB;AACD;AALC,cADY,YACKA,KAAsB;;;ACzCjC,IAAM,YAAY;EACxB,UAAU;EACV,UAAU;EACV,mBAAmB;EACnB,WAAW;EACX,WAAW;EACX,oBAAoB;EACpB,WAAW;EACX,WAAW;EACX,oBAAoB;EACpB,WAAW;EACX,WAAW;EACX,oBAAoB;EACpB,WAAW;EACX,WAAW;EACX,oBAAoB;EACpB,WAAW,CAAC;EACZ,WAAW;EACX,oBAAoB;;ACbL,SAAA,aAA+B,QAAgB,aAAqB;AACnF,SAAO,YAAY,SAAS,OAAO,UAAU;AAC9C;AAEM,SAAU,WAAW,QAAc;AACxC,SAAO,gBAAgB,UAAU,MAAM,QAAQ,OAAO,UAAU,KAAK,OAAO,WAAW,SAAS;AACjG;AAEO,IAAM,WAAqE;ACiD3E,IAAM,gBAAgB,EAAE,MAAM,CAAC,EAAE,OAAM,GAAI,EAAE,OAAM,GAAI,EAAE,QAAO,GAAI,EAAE,KAAI,CAAE,CAAC;AAC7E,IAAM,aAA8B,EAAE,MAAM,CAAC,eAAe,EAAE,OAAO,EAAE,IAAG,CAAE,GAAG,EAAE,MAAM,EAAE,IAAG,CAAE,CAAC,CAAC;AAChG,IAAM,eAAkC,EAAE,OAAe,CAAC,MAAM,aAAa,MAAM;AAE1E,SAAA,eAAe,QAAgB,SAA+C;AAC7F,QAAMC,OAAI,mCAAS,gBAAeC;AAClC,QAAM,UAAS,mCAAS,WAAU,CAAA;AAClC,MAAI;AAEJ,MAAI,WAAW,MAAM,GAAG;AACvB,aAAS,OAAO,WAAW,SAASD,IAAE,KAAK,OAAO,UAAU,IAAIA,IAAE,OAAM;;AAGzE,MAAI,CAAC,QAAQ;AAEZ,QAAI,aAAkD,QAAQ,CAAC,cAAc,cAAc,CAAC,GAAG;AAC9F,eAASA,IAAE,MAAM,CAACA,IAAE,OAAM,GAAIA,IAAE,OAAM,CAAE,CAAC;eAEzC,aAAyD,QAAQ,CAAC,oBAAoB,eAAe,CAAC,GACrG;AACD,eAASA,IAAE,OAAO,EAAE,GAAGA,IAAE,OAAM,GAAI,GAAGA,IAAE,OAAM,EAAE,CAAE;eACxC,aAAgD,QAAQ,CAAC,gBAAgB,UAAU,CAAC,GAAG;AACjG,eAASA,IAAE,MAAMA,IAAE,OAAM,CAAE;AAC3B,eAAS,OAAO,aAAc,OAA2B,OAAO,OAAO,UAAU,IAAI;eAC3E,aAA+B,QAAQ,CAAC,QAAQ,CAAC,GAAG;AAC9D,eAASA,IAAE,MAAM,CAACA,IAAE,OAAM,GAAIA,IAAE,OAAM,GAAIA,IAAE,OAAM,CAAE,CAAC;eAC3C,aAA6B,QAAQ,CAAC,WAAW,CAAC,GAAG;AAC/D,eAASA,IAAE,OAAO;QACjB,GAAGA,IAAE,OAAM;QACX,GAAGA,IAAE,OAAM;QACX,GAAGA,IAAE,OAAM;MACX,CAAA;IACF,WACS,aAAgC,QAAQ,CAAC,SAAS,CAAC,GAAG;AAC9D,eAASA,IAAE,MAAM,eAAe,OAAO,YAAYA,GAAC,CAAC;AACrD,eAAS,OAAO,OAAQ,OAA2B,OAAO,OAAO,IAAI,IAAI;eAC/D,OAAO,aAAa,SAAS;AACvC,eAASA,IAAE,MAAMA,IAAE,IAAG,CAAE;eACd,OAAO,aAAa,UAAU;AACxC,eAAS,qBAAqB,QAAQA,KAAG,MAAM;eACrC,OAAO,aAAa,UAAU;AACxC,eAAS,qBAAqB,QAAQA,KAAG,MAAM;eACrC,OAAO,aAAa,WAAW;AACzC,eAAS,WAAW,QAAQ,OAAO,UAAUA,IAAE,OAAO,QAAO,IAAKA,IAAE,QAAO;eACjE,OAAO,aAAa,QAAQ;AACtC,eAAS,WAAW,QAAQ,OAAO,OAAOA,IAAE,OAAO,KAAI,IAAKA,IAAE,KAAI;eACxD,OAAO,aAAa,UAAU;AACxC,eAAS,qBAAqB,QAAQA,KAAG,MAAM;eACrC,OAAO,aAAa,QAAQ;AACtC,eAAS;eACC,OAAO,aAAa,UAAU;AACxC,eAASA,IAAE,IAAG;eACJ,OAAO,aAAa,UAAU;AACxC,eAAS;;;AAIX,MAAI,CAAC,QAAQ;AACZ,aAASA,IAAE,IAAG;;AAGf,SAAO;AACR;AAEA,SAAS,qBACR,QACAA,IACA,QAA4C;AAE5C,MAAI,WAAW,OAAO,WAAU,EAAG,SAAS,UAAU;AACtD,MAAI;AACJ,MAAI;AACJ,MAAI,UAAU;AAEd,MAAI,aAA0D,QAAQ,CAAC,gBAAgB,oBAAoB,CAAC,GAAG;AAC9G,UAAM,WAAW,IAAI,UAAU;AAC/B,UAAM,WAAW,UAAU,oBAAoB,UAAU;AACzD,cAAU;aAEV,aAAmG,QAAQ;IAC1G;IACA;IACA;IACA;EACA,CAAA,GACA;AACD,UAAM,WAAW,IAAI,UAAU;AAC/B,UAAM,WAAW,UAAU,qBAAqB,UAAU;AAC1D,cAAU;aAEV,aAEE,QAAQ;IACT;IACA;IACA;IACA;IACA;EACA,CAAA,GACA;AACD,UAAM,WAAW,IAAI,UAAU;AAC/B,UAAM,WAAW,UAAU,qBAAqB,UAAU;AAC1D,cAAU,aAAa,QAAQ,CAAC,kBAAkB,sBAAsB,CAAC;aAEzE,aAAmF,QAAQ;IAC1F;IACA;IACA;IACA;EACA,CAAA,GACA;AACD,UAAM,WAAW,IAAI,UAAU;AAC/B,UAAM,WAAW,UAAU,qBAAqB,UAAU;AAC1D,cAAU;aAEV,aAOE,QAAQ;IACT;IACA;IACA;IACA;IACA;IACA;EACA,CAAA,GACA;AACD,UAAM,WAAW,IAAI,UAAU;AAC/B,UAAM,WAAW,UAAU,qBAAqB,UAAU;aAE1D,aASC,QACA;IACC;IACA;IACA;IACA;IACA;IACA;IACA;EACA,CAAA,GAED;AACD,eAAW,YAAY,aAAa,QAAQ,CAAC,eAAe,mBAAmB,CAAC;AAChF,UAAM,WAAW,IAAI,OAAO;AAC5B,UAAM,OAAO;AACb,cAAU;aACA,aAAoD,QAAQ,CAAC,aAAa,iBAAiB,CAAC,GAAG;AACzG,UAAM;AACN,UAAM;AACN,cAAU;SACJ;AACN,UAAM,OAAO;AACb,UAAM,OAAO;;AAGd,MAAI,SAAS,WAAW,SAAQ,iCAAQ,UAASA,GAAE,OAAO,OAAM,IAAKA,GAAE,OAAM;AAC7E,WAAS,OAAO,IAAI,GAAG,EAAE,IAAI,GAAG;AAChC,SAAO,UAAU,OAAO,IAAG,IAAK;AACjC;AAEA,SAAS,qBACR,QACAA,IACA,QAA4C;AAE5C,QAAM,WAAW,OAAO,WAAU,EAAG,SAAS,UAAU;AACxD,QAAM,MAAM,WAAW,KAAK,UAAU;AACtC,QAAM,MAAM,WAAW,UAAU,qBAAqB,UAAU;AAEhE,QAAM,SAAS,WAAW,SAAQ,iCAAQ,UAASA,GAAE,OAAO,OAAM,IAAKA,GAAE,OAAM;AAC/E,SAAO,OAAO,IAAI,GAAG,EAAE,IAAI,GAAG;AAC/B;AAEA,SAAS,qBACR,QACAA,IACA,QAA4C;AAE5C,MAAI,aAA2D,QAAQ,CAAC,QAAQ,CAAC,GAAG;AACnF,WAAOA,GAAE,OAAM,EAAG,KAAI;;AAGvB,MAAI;AACJ,MAAI;AACJ,MAAI,QAAQ;AAEZ,MAAI,aAA+C,QAAQ,CAAC,aAAa,YAAY,CAAC,GAAG;AACxF,UAAM,OAAO;aAEb,aAA0D,QAAQ,CAAC,gBAAgB,oBAAoB,CAAC,GACvG;AACD,UAAM,OAAO,UAAU,UAAU;aACvB,aAAoD,QAAQ,CAAC,aAAa,iBAAiB,CAAC,GAAG;AACzG,QAAI,OAAO,aAAa,YAAY;AACnC,YAAM,UAAU;eACN,OAAO,aAAa,cAAc;AAC5C,YAAM,UAAU;eACN,OAAO,aAAa,QAAQ;AACtC,YAAM,UAAU;WACV;AACN,YAAM,UAAU;;;AAIlB,MACC,aAAkE,QAAQ;IACzE;IACA;IACA;EACA,CAAA,GACA;AACD,UAAM,OAAO;AACb,YAAQ;;AAGT,MAAI,aAAkC,QAAQ,CAAC,gBAAgB,CAAC,GAAG;AAClE,YAAQ;AACR,UAAM,OAAO;;AAGd,MAAI,SAAS,WAAW,SAAQ,iCAAQ,UAASA,GAAE,OAAO,OAAM,IAAKA,GAAE,OAAM;AAC7E,WAAS,QAAQ,OAAO,MAAM,KAAK,IAAI;AACvC,SAAO,OAAO,QAAQ,OAAO,OAAO,GAAG,IAAI,MAAM,OAAO,IAAI,GAAG,IAAI;AACpE;AC5RA,SAAS,WAAW,WAAuB;AAC1C,SAAO,QAAQ,SAAS,IAAI,gBAAgB,SAAS,IAAI,sBAAsB,SAAS;AACzF;AAEA,SAAS,cACR,SACA,aACA,YACA,SAAoC;AAEpC,QAAM,gBAA8C,CAAA;AAEpD,aAAW,CAAC,KAAK,QAAQ,KAAK,OAAO,QAAQ,OAAO,GAAG;AACtD,QAAI,CAAC,GAAG,UAAU,MAAM,KAAK,CAAC,GAAG,UAAU,GAAG,KAAK,CAAC,GAAG,UAAU,IAAI,OAAO,KAAK,OAAO,aAAa,UAAU;AAC9G,YAAME,WAAU,QAAQ,QAAQ,KAAK,OAAO,QAAQ,IAAI,WAAW,QAAQ,IAAI;AAC/E,oBAAc,GAAG,IAAI,cAAcA,UAAS,YAAY,GAAG,KAAK,CAAA,GAAI,YAAY,OAAO;AACvF;;AAGD,UAAM,aAAa,YAAY,GAAG;AAClC,QAAI,eAAe,UAAa,OAAO,eAAe,YAAY;AACjE,oBAAc,GAAG,IAAI;AACrB;;AAGD,UAAM,SAAS,GAAG,UAAU,MAAM,IAAI,WAAW;AACjD,UAAM,SAAS,SAAS,eAAe,QAAQ,OAAO,IAAI,EAAE,IAAG;AAC/D,UAAM,UAAU,OAAO,eAAe,aAAa,WAAW,MAAM,IAAI;AAExE,QAAI,WAAW,MAAM,MAAM,GAAG;AAC7B;WACM;AACN,oBAAc,GAAG,IAAI;;AAGtB,QAAI,QAAQ;AACX,UAAI,WAAW,SAAS,MAAM,GAAG;AAChC,sBAAc,GAAG,IAAI,cAAc,GAAG,EAAG,SAAQ;;AAGlD,UAAI,WAAW,SAAS,MAAM,GAAG;AAChC,sBAAc,GAAG,IAAI,cAAc,GAAG,EAAG,SAAQ;;;;AAKpD,SAAO,EAAE,OAAO,aAAa;AAC9B;AAEA,SAAS,WAAW,OAAoB,SAAoC;AAC3E,QAAM,OAAgB,mCAAS,gBAAe;AAC9C,SAAO,IAAI,KAAK,MAAM,UAAU;AACjC;AAEA,IAAM,mBAA+B;EACpC,OAAO,MAAM;EACb,UAAU,MAAM;EAChB,UAAU,CAAC,WAAW,CAAC,OAAO;;AAG/B,IAAM,mBAA+B;EACpC,OAAO,CAAC,WAAM;;AAAK,aAAAC,MAAA,iCAAQ,cAAR,gBAAAA,IAAmB,UAAS,cAAY,sCAAQ,sBAAR,mBAA2B,UAAS;;EAC/F,UAAU,CAAC,WAAW,CAAC,OAAO,WAAY,OAAO,WAAW,OAAO;EACnE,UAAU,CAAC,WAAW,CAAC,OAAO;;AAG/B,IAAM,mBAA+B;EACpC,OAAO,CAAC,WAAM;;AAAK,aAAAA,MAAA,iCAAQ,cAAR,gBAAAA,IAAmB,UAAS,cAAY,sCAAQ,sBAAR,mBAA2B,UAAS;;EAC/F,UAAU,MAAM;EAChB,UAAU,CAAC,WAAW,CAAC,OAAO;;IAGlB,qBAAyC,CACrD,QACA,WACG;AACH,MAAI,SAAS,MAAM,GAAG;AACrB,WAAO,WAAW,MAAM;;AAEzB,QAAM,UAAU,WAAW,MAAM;AACjC,SAAO,cAAc,SAAS,UAAU,CAAA,GAAI,gBAAgB;AAC7D;IAEa,qBAAyC,CACrD,QACA,WACG;AACH,QAAM,UAAU,WAAW,MAAM;AACjC,SAAO,cAAc,SAAS,UAAU,CAAA,GAAI,gBAAgB;AAC7D;IAEa,qBAAyC,CACrD,QACA,WACG;AACH,QAAM,UAAU,WAAW,MAAM;AACjC,SAAO,cAAc,SAAS,UAAU,CAAA,GAAI,gBAAgB;AAC7D;AAEM,SAAU,oBAAoB,SAAoC;AACvE,QAAMC,sBAAyC,CAC9C,QACA,WACG;AACH,QAAI,SAAS,MAAM,GAAG;AACrB,aAAO,WAAW,QAAQ,OAAO;;AAElC,UAAM,UAAU,WAAW,MAAM;AACjC,WAAO,cAAc,SAAS,UAAU,CAAA,GAAI,kBAAkB,OAAO;EACtE;AAEA,QAAMC,sBAAyC,CAC9C,QACA,WACG;AACH,UAAM,UAAU,WAAW,MAAM;AACjC,WAAO,cAAc,SAAS,UAAU,CAAA,GAAI,kBAAkB,OAAO;EACtE;AAEA,QAAMC,sBAAyC,CAC9C,QACA,WACG;AACH,UAAM,UAAU,WAAW,MAAM;AACjC,WAAO,cAAc,SAAS,UAAU,CAAA,GAAI,kBAAkB,OAAO;EACtE;AAEA,SAAO,EAAE,oBAAAF,qBAAoB,oBAAAC,qBAAoB,oBAAAC,oBAAkB;AACpE;", "names": ["_a", "z", "zod", "columns", "_a", "createSelectSchema", "createInsertSchema", "createUpdateSchema"]}
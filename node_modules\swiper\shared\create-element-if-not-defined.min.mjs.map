{"version": 3, "file": "create-element-if-not-defined.mjs.mjs", "names": ["elementChildren", "createElement", "createElementIfNotDefined", "swiper", "originalParams", "params", "checkProps", "createElements", "Object", "keys", "for<PERSON>ach", "key", "auto", "element", "el", "className", "append"], "sources": ["0"], "mappings": "YAAcA,qBAAsBC,kBAAqB,kBAEzD,SAASC,0BAA0BC,EAAQC,EAAgBC,EAAQC,GAejE,OAdIH,EAAOE,OAAOE,gBAChBC,OAAOC,KAAKH,GAAYI,SAAQC,IAC9B,IAAKN,EAAOM,KAAwB,IAAhBN,EAAOO,KAAe,CACxC,IAAIC,EAAUb,gBAAgBG,EAAOW,GAAI,IAAIR,EAAWK,MAAQ,GAC3DE,IACHA,EAAUZ,cAAc,MAAOK,EAAWK,IAC1CE,EAAQE,UAAYT,EAAWK,GAC/BR,EAAOW,GAAGE,OAAOH,IAEnBR,EAAOM,GAAOE,EACdT,EAAeO,GAAOE,CACxB,KAGGR,CACT,QAESH"}
import{a as getWindow}from"../shared/ssr-window.esm.min.mjs";function History(e){let{swiper:t,extendParams:a,on:s}=e;a({history:{enabled:!1,root:"",replaceState:!1,key:"slides",keepQuery:!1}});let r=!1,i={};const l=e=>e.toString().replace(/\s+/g,"-").replace(/[^\w-]+/g,"").replace(/--+/g,"-").replace(/^-+/,"").replace(/-+$/,""),o=e=>{const t=getWindow();let a;a=e?new URL(e):t.location;const s=a.pathname.slice(1).split("/").filter((e=>""!==e)),r=s.length;return{key:s[r-2],value:s[r-1]}},n=(e,a)=>{const s=getWindow();if(!r||!t.params.history.enabled)return;let i;i=t.params.url?new URL(t.params.url):s.location;const o=t.virtual&&t.params.virtual.enabled?t.slidesEl.querySelector(`[data-swiper-slide-index="${a}"]`):t.slides[a];let n=l(o.getAttribute("data-history"));if(t.params.history.root.length>0){let a=t.params.history.root;"/"===a[a.length-1]&&(a=a.slice(0,a.length-1)),n=`${a}/${e?`${e}/`:""}${n}`}else i.pathname.includes(e)||(n=`${e?`${e}/`:""}${n}`);t.params.history.keepQuery&&(n+=i.search);const p=s.history.state;p&&p.value===n||(t.params.history.replaceState?s.history.replaceState({value:n},null,n):s.history.pushState({value:n},null,n))},p=(e,a,s)=>{if(a)for(let r=0,i=t.slides.length;r<i;r+=1){const i=t.slides[r];if(l(i.getAttribute("data-history"))===a){const a=t.getSlideIndex(i);t.slideTo(a,e,s)}}else t.slideTo(0,e,s)},d=()=>{i=o(t.params.url),p(t.params.speed,i.value,!1)};s("init",(()=>{t.params.history.enabled&&(()=>{const e=getWindow();if(t.params.history){if(!e.history||!e.history.pushState)return t.params.history.enabled=!1,void(t.params.hashNavigation.enabled=!0);r=!0,i=o(t.params.url),i.key||i.value?(p(0,i.value,t.params.runCallbacksOnInit),t.params.history.replaceState||e.addEventListener("popstate",d)):t.params.history.replaceState||e.addEventListener("popstate",d)}})()})),s("destroy",(()=>{t.params.history.enabled&&(()=>{const e=getWindow();t.params.history.replaceState||e.removeEventListener("popstate",d)})()})),s("transitionEnd _freeModeNoMomentumRelease",(()=>{r&&n(t.params.history.key,t.activeIndex)})),s("slideChange",(()=>{r&&t.params.cssMode&&n(t.params.history.key,t.activeIndex)}))}export{History as default};
//# sourceMappingURL=history.min.mjs.map
import { motion } from "framer-motion";
import { useInView } from "framer-motion";
import { useRef } from "react";

export default function ProblemSolutionSection() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  return (
    <section
      className='py-32 px-6 lg:px-12 bg-gradient-to-br from-gray-50 to-white'
      ref={ref}
    >
      <div className='max-w-7xl mx-auto'>
        <div className='grid lg:grid-cols-2 gap-24 items-start'>
          {/* Problem */}
          <motion.div
            className='space-y-8'
            initial={{ opacity: 0, x: -50 }}
            animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -50 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
          >
            <div className='space-y-6'>
              <motion.div
                className='inline-block px-4 py-2 bg-red-100 text-red-600 rounded-full text-sm font-semibold'
                initial={{ scale: 0 }}
                animate={isInView ? { scale: 1 } : { scale: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                The Problem
              </motion.div>

              <h2 className='text-3xl md:text-4xl font-bold text-gray-900 leading-tight'>
                Struggling with <span className='text-red-500'>Low CTR</span> or
                Bland Visuals?
              </h2>

              <p className='text-lg text-gray-600 leading-relaxed'>
                In 2025, attention is the currency. Without a powerful
                thumbnail, your video is invisible — even with great content.
                Most creators struggle with:
              </p>

              <ul className='space-y-4'>
                {[
                  "Thumbnails that blend into the crowd",
                  "Low click-through rates despite good content",
                  "Spending hours on design with poor results",
                  "Not understanding what makes people click",
                ].map((item, index) => (
                  <motion.li
                    key={index}
                    className='flex items-center space-x-3 text-gray-600'
                    initial={{ opacity: 0, x: -20 }}
                    animate={
                      isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -20 }
                    }
                    transition={{ duration: 0.5, delay: 0.4 + index * 0.1 }}
                  >
                    <div className='w-2 h-2 bg-red-500 rounded-full'></div>
                    <span>{item}</span>
                  </motion.li>
                ))}
              </ul>
            </div>
          </motion.div>

          {/* Solution */}
          <motion.div
            className='space-y-8'
            initial={{ opacity: 0, x: 50 }}
            animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: 50 }}
            transition={{ duration: 0.8, ease: "easeOut", delay: 0.2 }}
          >
            <div className='glassmorphic rounded-3xl p-8 space-y-6'>
              <motion.div
                className='inline-block px-4 py-2 bg-green-100 text-green-600 rounded-full text-sm font-semibold'
                initial={{ scale: 0 }}
                animate={isInView ? { scale: 1 } : { scale: 0 }}
                transition={{ duration: 0.5, delay: 0.4 }}
              >
                My Solution
              </motion.div>

              <h3 className='text-3xl md:text-4xl font-bold text-gray-900 leading-tight'>
                Thumbnails That{" "}
                <span className='text-gradient'>Interrupt the Scroll</span>
              </h3>

              <p className='text-lg text-gray-600 leading-relaxed'>
                I make thumbnails that{" "}
                <span className='font-semibold text-[#FF4500]'>
                  interrupt the scroll
                </span>
                ,{" "}
                <span className='font-semibold text-[#FF4500]'>
                  evoke emotion
                </span>
                , and{" "}
                <span className='font-semibold text-[#FF4500]'>
                  spark curiosity
                </span>
                . It's not about art — it's about{" "}
                <span className='font-semibold text-[#FF4500]'>strategy</span>.
              </p>

              <ul className='space-y-4'>
                {[
                  "Psychology-driven design that triggers clicks",
                  "Algorithm-optimized visuals that boost visibility",
                  "Fast turnaround without compromising quality",
                  "Data-backed strategies for maximum impact",
                ].map((item, index) => (
                  <motion.li
                    key={index}
                    className='flex items-center space-x-3 text-gray-600'
                    initial={{ opacity: 0, x: 20 }}
                    animate={
                      isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: 20 }
                    }
                    transition={{ duration: 0.5, delay: 0.6 + index * 0.1 }}
                  >
                    <div className='w-8 h-8 bg-gradient-to-r from-[#FF4500] to-[#FF6B35] rounded-full flex items-center justify-center'>
                      <i className='fas fa-check text-white text-sm'></i>
                    </div>
                    <span>{item}</span>
                  </motion.li>
                ))}
              </ul>

              <motion.a
                href='#contact'
                className='inline-flex items-center px-8 py-4 bg-gradient-to-r from-[#FF4500] to-[#FF6B35] text-white font-semibold rounded-full hover:shadow-lg transition-all duration-300'
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.98 }}
              >
                <span className='mr-3'>Start Converting More Views</span>
                <i className='fas fa-arrow-right'></i>
              </motion.a>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}

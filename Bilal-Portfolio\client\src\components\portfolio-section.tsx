import { motion } from 'framer-motion';
import { useInView } from 'framer-motion';
import { useRef } from 'react';

export default function PortfolioSection() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  const portfolioItems = [
    {
      image: "https://pixabay.com/get/g80c100e00a6a74b5128cd693410e3f6b7916057cc195e3beb07eff9089f1d8281a52a46aa9bc6f1587f6c1a07756262b15110765167fe6d7cd0e370b7460ebf6_1280.jpg",
      title: "Tech Review Thumbnail",
      category: "Tech",
      categoryColor: "bg-orange-100 text-orange-600"
    },
    {
      image: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&h=450",
      title: "Fitness Challenge",
      category: "Fitness",
      categoryColor: "bg-green-100 text-green-600"
    },
    {
      image: "https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&h=450",
      title: "Viral Challenge",
      category: "Entertainment",
      categoryColor: "bg-purple-100 text-purple-600"
    },
    {
      image: "https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&h=450",
      title: "Tutorial Series",
      category: "Education",
      categoryColor: "bg-blue-100 text-blue-600"
    },
    {
      image: "https://images.unsplash.com/photo-1542751371-adc38448a05e?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&h=450",
      title: "Gaming Highlights",
      category: "Gaming",
      categoryColor: "bg-red-100 text-red-600"
    },
    {
      image: "https://images.unsplash.com/photo-1504805572947-34fad45aed93?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&h=450",
      title: "Daily Vlog",
      category: "Lifestyle",
      categoryColor: "bg-pink-100 text-pink-600"
    },
    {
      image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&h=450",
      title: "Business Tips",
      category: "Business",
      categoryColor: "bg-yellow-100 text-yellow-600"
    },
    {
      image: "https://images.unsplash.com/photo-1488646953014-85cb44e25828?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&h=450",
      title: "Travel Guide",
      category: "Travel",
      categoryColor: "bg-teal-100 text-teal-600"
    }
  ];

  return (
    <section id="portfolio" className="py-32 px-6 lg:px-12 bg-white" ref={ref}>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div 
          className="text-center space-y-8 mb-20"
          initial={{ opacity: 0, y: 50 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight">
            <span className="text-gradient">Thumbnails That Perform</span>
          </h2>
          <p className="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            See how my designs improved CTR and visibility for creators across niches.
          </p>
        </motion.div>
        
        {/* Portfolio Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8 mb-16">
          {portfolioItems.map((item, index) => (
            <motion.div
              key={index}
              className="glassmorphic rounded-3xl p-6 magnetic-hover group"
              initial={{ opacity: 0, y: 30 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
              transition={{ duration: 0.5, delay: 0.1 * index }}
            >
              <img 
                src={item.image}
                alt={item.title}
                className="rounded-2xl w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
              />
              <div className="mt-4 space-y-2">
                <div className="flex items-center justify-between">
                  <span className={`${item.categoryColor} px-3 py-1 rounded-full text-sm font-medium`}>
                    {item.category}
                  </span>
                  <i className="fas fa-external-link-alt text-gray-400 group-hover:text-[#FF4500] transition-colors"></i>
                </div>
                <h4 className="font-semibold text-gray-900 group-hover:text-[#FF4500] transition-colors">
                  {item.title}
                </h4>
              </div>
            </motion.div>
          ))}
        </div>
        
        {/* CTA Button */}
        <motion.div 
          className="text-center"
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.6, delay: 0.8 }}
        >
          <a 
            href="#contact" 
            className="inline-flex items-center px-12 py-6 bg-gradient-to-r from-[#FF4500] to-[#FF6B35] text-white font-semibold text-lg rounded-2xl hover:shadow-2xl hover:scale-105 transition-all duration-300"
          >
            <span className="mr-3">Let's Work Together</span>
            <i className="fas fa-arrow-right"></i>
          </a>
        </motion.div>
      </div>
    </section>
  );
}

import { motion } from "framer-motion";
import { useInView } from "framer-motion";
import { useRef, useState } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, EffectCoverflow, Pagination } from "swiper/modules";
import "swiper/css";
import "swiper/css/effect-coverflow";
import "swiper/css/pagination";

export default function PortfolioSection() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });
  const [isHovered, setIsHovered] = useState(false);

  const portfolioItems = [
    {
      image: "/thumbnails/1.png",
      title: "Viral Content Thumbnail",
      category: "Entertainment",
      categoryColor: "bg-purple-100 text-purple-600",
    },
    {
      image: "/thumbnails/2.png",
      title: "Tech Review Design",
      category: "Technology",
      categoryColor: "bg-blue-100 text-blue-600",
    },
    {
      image: "/thumbnails/3.png",
      title: "Gaming Content",
      category: "Gaming",
      categoryColor: "bg-red-100 text-red-600",
    },
    {
      image: "/thumbnails/4.png",
      title: "Educational Series",
      category: "Education",
      categoryColor: "bg-green-100 text-green-600",
    },
    {
      image: "/thumbnails/5.png",
      title: "Lifestyle Vlog",
      category: "Lifestyle",
      categoryColor: "bg-pink-100 text-pink-600",
    },
    {
      image: "/thumbnails/6.png",
      title: "Business Content",
      category: "Business",
      categoryColor: "bg-yellow-100 text-yellow-600",
    },
    {
      image: "/thumbnails/11.png",
      title: "Creative Design",
      category: "Creative",
      categoryColor: "bg-indigo-100 text-indigo-600",
    },
    {
      image: "/thumbnails/Alex1.png",
      title: "Alex Series - Part 1",
      category: "Series",
      categoryColor: "bg-teal-100 text-teal-600",
    },
    {
      image: "/thumbnails/Alex2.png",
      title: "Alex Series - Part 2",
      category: "Series",
      categoryColor: "bg-teal-100 text-teal-600",
    },
    {
      image: "/thumbnails/Alex3.png",
      title: "Alex Series - Part 3",
      category: "Series",
      categoryColor: "bg-teal-100 text-teal-600",
    },
    {
      image: "/thumbnails/Beast.png",
      title: "Beast Mode Content",
      category: "Fitness",
      categoryColor: "bg-orange-100 text-orange-600",
    },
    {
      image: "/thumbnails/Boom or Bust.png",
      title: "Boom or Bust",
      category: "Finance",
      categoryColor: "bg-emerald-100 text-emerald-600",
    },
    {
      image: "/thumbnails/Colourful.png",
      title: "Colorful Design",
      category: "Art",
      categoryColor: "bg-rose-100 text-rose-600",
    },
    {
      image: "/thumbnails/FUKRA THUMBNAIL 2.png",
      title: "Fukra Series - Part 2",
      category: "Comedy",
      categoryColor: "bg-amber-100 text-amber-600",
    },
    {
      image: "/thumbnails/FUKRA THUMBNAIL 3.png",
      title: "Fukra Series - Part 3",
      category: "Comedy",
      categoryColor: "bg-amber-100 text-amber-600",
    },
    {
      image: "/thumbnails/Fredom.png",
      title: "Freedom Theme",
      category: "Motivational",
      categoryColor: "bg-cyan-100 text-cyan-600",
    },
    {
      image: "/thumbnails/Trump.png",
      title: "Political Commentary",
      category: "News",
      categoryColor: "bg-slate-100 text-slate-600",
    },
    {
      image: "/thumbnails/Trump Economy.png",
      title: "Economy Analysis",
      category: "Finance",
      categoryColor: "bg-emerald-100 text-emerald-600",
    },
    {
      image: "/thumbnails/Trump Mass Firings.png",
      title: "Breaking News",
      category: "News",
      categoryColor: "bg-slate-100 text-slate-600",
    },
    {
      image: "/thumbnails/Then Now.png",
      title: "Then vs Now",
      category: "Comparison",
      categoryColor: "bg-violet-100 text-violet-600",
    },
  ];

  return (
    <section
      id='portfolio'
      className='py-32 px-6 lg:px-12 bg-gradient-to-br from-gray-50 to-white relative overflow-hidden'
      ref={ref}
    >
      {/* Background Effects */}
      <div className='absolute inset-0 bg-gradient-to-r from-blue-50/30 to-purple-50/30'></div>
      <div className='absolute top-20 left-10 w-72 h-72 bg-gradient-to-r from-blue-400/10 to-purple-400/10 rounded-full blur-3xl'></div>
      <div className='absolute bottom-20 right-10 w-96 h-96 bg-gradient-to-r from-orange-400/10 to-pink-400/10 rounded-full blur-3xl'></div>

      <div className='max-w-7xl mx-auto relative z-10'>
        {/* Header */}
        <motion.div
          className='text-center space-y-8 mb-20'
          initial={{ opacity: 0, y: 50 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          <h2 className='text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight'>
            <span className='text-gradient'>Thumbnails That Perform</span>
          </h2>
          <p className='text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed'>
            See how my designs improved CTR and visibility for creators across
            niches.
          </p>
        </motion.div>

        {/* Smooth Portfolio Slider */}
        <motion.div
          className='relative'
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          <Swiper
            grabCursor={true}
            centeredSlides={false}
            slidesPerView={"auto"}
            spaceBetween={40}
            speed={1200}
            autoplay={{
              delay: 2500,
              disableOnInteraction: false,
              pauseOnMouseEnter: true,
            }}
            loop={true}
            modules={[Autoplay]}
            className='portfolio-swiper'
            breakpoints={{
              320: {
                slidesPerView: 1,
                spaceBetween: 30,
              },
              640: {
                slidesPerView: 1.2,
                spaceBetween: 35,
              },
              768: {
                slidesPerView: 1.8,
                spaceBetween: 40,
              },
              1024: {
                slidesPerView: 2.5,
                spaceBetween: 45,
              },
              1280: {
                slidesPerView: 3.2,
                spaceBetween: 50,
              },
            }}
          >
            {portfolioItems.map((item, index) => (
              <SwiperSlide key={index}>
                <motion.div
                  className='group cursor-pointer'
                  whileHover={{ scale: 1.05, y: -5 }}
                  transition={{ duration: 0.4, ease: "easeOut" }}
                >
                  <img
                    src={item.image}
                    alt={item.title}
                    className='w-full h-auto rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:scale-[1.02]'
                    loading='lazy'
                  />
                </motion.div>
              </SwiperSlide>
            ))}
          </Swiper>
        </motion.div>

        {/* CTA Button */}
        <motion.div
          className='text-center mt-16'
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.6, delay: 0.8 }}
        >
          <a
            href='#contact'
            className='inline-flex items-center px-12 py-6 bg-gradient-to-r from-[#FF4500] to-[#FF6B35] text-white font-semibold text-lg rounded-2xl hover:shadow-2xl hover:scale-105 transition-all duration-300 relative overflow-hidden group'
          >
            <div className='absolute inset-0 bg-gradient-to-r from-[#FF6B35] to-[#FF4500] opacity-0 group-hover:opacity-100 transition-opacity duration-300'></div>
            <span className='mr-3 relative z-10'>Let's Work Together</span>
            <i className='fas fa-arrow-right relative z-10 group-hover:translate-x-1 transition-transform duration-300'></i>
          </a>
        </motion.div>
      </div>
    </section>
  );
}
